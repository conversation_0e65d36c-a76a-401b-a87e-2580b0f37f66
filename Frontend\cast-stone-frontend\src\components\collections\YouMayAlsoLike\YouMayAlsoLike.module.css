/* You May Also Like Section Styles */
.youMayAlsoLike {
  padding: 4rem 0;
  background: #ffffff;
  color: white;
  position: relative;
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem;
  background: #1e3a5f;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.header {
  margin-bottom: 2rem;
}

.title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  letter-spacing: 0.02em;
  color: #ffffff;
  text-transform: none;
}

.description {
  font-size: 0.95rem;
  color: #b8d4f0;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 500px;
}

.mainContent {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 3rem;
  align-items: center;
}

.leftContent {
  display: flex;
  flex-direction: column;
}

.rightContent {
  position: relative;
}

.carouselContainer {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
}

.collectionsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  flex: 1;
  min-height: 300px;
}

.collectionCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  height: 300px;
}

.collectionCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
}

.cardImageContainer {
  position: relative;
  height: 100%;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
}

.cardImage {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  object-fit: none;
  display: block;
  margin: 0 auto;
  transition: transform 0.3s ease;
}

.collectionCard:hover .cardImage {
  transform: scale(1.03);
}

/* Stats Cards for Collection Names */
.statsContainer {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.statCard:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.statCard.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
}

.statLabel {
  font-size: 0.85rem;
  color: #b8d4f0;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statValue {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.2;
}

.navButton {
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  flex-shrink: 0;
}

.navButton:hover:not(:disabled) {
  background: #2563eb;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.navButton:disabled {
  background: #4a5568;
  cursor: not-allowed;
  opacity: 0.5;
  box-shadow: none;
}

.navButton svg {
  width: 16px;
  height: 16px;
}

.prevButton {
  order: -1;
}

.nextButton {
  order: 1;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.primaryButton {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.primaryButton:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.secondaryButton {
  background: transparent;
  color: #b8d4f0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .youMayAlsoLike {
    padding: 3rem 0;
  }

  .container {
    padding: 2rem;
  }

  .mainContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .statsContainer {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .title {
    font-size: 1.6rem;
  }

  .description {
    font-size: 0.9rem;
  }

  .cardImageContainer {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .youMayAlsoLike {
    padding: 2rem 0;
  }

  .container {
    padding: 1.5rem;
    margin: 0 1rem;
  }

  .title {
    font-size: 1.4rem;
  }

  .description {
    font-size: 0.85rem;
    margin-bottom: 1.5rem;
  }

  .statsContainer {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .statCard {
    padding: 0.75rem;
  }

  .carouselContainer {
    flex-direction: column;
    gap: 1rem;
  }

  .cardImageContainer {
    height: 200px;
  }

  .actionButtons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .primaryButton,
  .secondaryButton {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
    text-align: center;
    justify-content: center;
  }

  .navButton {
    width: 36px;
    height: 36px;
  }

  .navButton svg {
    width: 14px;
    height: 14px;
  }

  .prevButton,
  .nextButton {
    order: 0;
  }

  .carouselContainer {
    position: relative;
  }

  .prevButton {
    position: absolute;
    top: 50%;
    left: -8px;
    transform: translateY(-50%);
    z-index: 3;
  }

  .nextButton {
    position: absolute;
    top: 50%;
    right: -8px;
    transform: translateY(-50%);
    z-index: 3;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem;
    margin: 0 0.5rem;
  }

  .title {
    font-size: 1.2rem;
  }

  .description {
    font-size: 0.8rem;
  }

  .cardImageContainer {
    height: 180px;
  }

  .statCard {
    padding: 0.5rem;
  }

  .statLabel {
    font-size: 0.75rem;
  }

  .statValue {
    font-size: 0.9rem;
  }

  .navButton {
    width: 32px;
    height: 32px;
  }

  .navButton svg {
    width: 12px;
    height: 12px;
  }

  .prevButton {
    left: -6px;
  }

  .nextButton {
    right: -6px;
  }
}
