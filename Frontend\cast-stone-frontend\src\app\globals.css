@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #1f2937;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #1f2937;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Swiper Pagination Styles */
.swiper-pagination-bullet,
.swiper-pagination-bullet-active {
  background: #fff !important;
}

/* Remove default Swiper shadows for 3D effect */
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
  background-image: none !important;
}
