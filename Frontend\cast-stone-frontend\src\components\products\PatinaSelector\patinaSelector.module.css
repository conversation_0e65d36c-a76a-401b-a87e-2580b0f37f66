/* Patina Selector - Smaller Version */
.patinaSelector {
  margin: 1rem 0;
  padding: 1rem;
  border: 1px solid #ddd;
  background: #fafafa;
}

.selectorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #2563eb;
}

.selectorTitle {
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.selectedPatina {
  font-size: 0.85rem;
  font-weight: 600;
  color: #2563eb;
  background: white;
  padding: 0.4rem 0.75rem;
  border: 1px solid #2563eb;
}

/* Patina Grid */
.patinaGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.patinaOption {
  background: white;
  border: 1px solid #ddd;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
  min-height: 80px;
}

.patinaOption:hover {
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
}

.patinaOption.selected {
  border-color: #2563eb;
  border-width: 2px;
  background: #eff6ff;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

.patinaColor {
  width: 30px;
  height: 30px;
  border: 1px solid #333;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.patinaName {
  font-size: 0.75rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.1;
}

.patinaOption.selected .patinaName {
  font-weight: 700;
}

/* Patina Note */
.patinaNote {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 0.75rem;
  margin-top: 0.75rem;
}

.patinaNote p {
  margin: 0;
  font-size: 0.8rem;
  color: #856404;
  line-height: 1.3;
}

.patinaNote strong {
  color: #533f03;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .patinaSelector {
    padding: 0.75rem;
  }

  .selectorHeader {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .patinaGrid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
  }

  .patinaOption {
    padding: 0.5rem;
    min-height: 70px;
    gap: 0.4rem;
  }

  .patinaColor {
    width: 25px;
    height: 25px;
  }

  .patinaName {
    font-size: 0.7rem;
  }

  .selectorTitle {
    font-size: 0.95rem;
  }

  .selectedPatina {
    font-size: 0.8rem;
    padding: 0.35rem 0.6rem;
  }
}

@media (max-width: 480px) {
  .patinaGrid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  }

  .patinaOption {
    min-height: 60px;
  }

  .patinaNote {
    padding: 0.5rem;
  }

  .patinaNote p {
    font-size: 0.75rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .patinaOption {
    transition: none;
  }

  .patinaOption:hover {
    transform: none;
  }
}
