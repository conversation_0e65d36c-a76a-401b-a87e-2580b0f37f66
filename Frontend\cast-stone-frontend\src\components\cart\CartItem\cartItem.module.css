/* Cart Item Styles - Magazine/Editorial Theme */
.cartItem {
  display: grid;
  grid-template-columns: 120px 1fr auto auto auto;
  gap: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  align-items: start;
  transition: box-shadow 0.3s ease;
}

.cartItem:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Product Image */
.imageContainer {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Product Details */
.productDetails {
  min-width: 0; /* Allow text truncation */
}

.productName {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.productDescription {
  color: #4b5563;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 0.75rem 0;
}

.productMeta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.unitPrice {
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.wholesaleLabel {
  color: #059669;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.collection {
  color: #2563eb;
  font-size: 0.85rem;
  font-style: italic;
}

.stockStatus {
  margin-top: 0.5rem;
}

.inStock {
  color: #059669;
  font-size: 0.85rem;
  font-weight: 600;
}

.outOfStock {
  color: #dc2626;
  font-size: 0.85rem;
  font-weight: 600;
}

/* Quantity Section */
.quantitySection {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
}

.quantityLabel {
  color: #1f2937;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
}

.quantityControls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 0.25rem;
}

.quantityBtn {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  color: #1f2937;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quantityBtn:hover:not(:disabled) {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.quantityBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #1f2937;
  font-size: 1rem;
}

.updating {
  color: #2563eb;
  font-size: 0.75rem;
  font-style: italic;
}

/* Price Section */
.priceSection {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
  min-width: 120px;
}

.itemTotal {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
}

.priceBreakdown {
  color: #4b5563;
  font-size: 0.85rem;
}

/* Remove Section */
.removeSection {
  display: flex;
  align-items: flex-start;
}

.removeBtn {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  color: #dc2626;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.removeBtn:hover:not(:disabled) {
  background: #fee2e2;
  color: #b91c1c;
}

.removeBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.removeBtn svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

.removing {
  font-size: 1.2rem;
  font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cartItem {
    grid-template-columns: 80px 1fr;
    grid-template-areas: 
      "image details"
      "image quantity"
      "image price"
      "remove remove";
    gap: 1rem;
    padding: 1rem;
  }

  .imageContainer {
    grid-area: image;
    width: 80px;
    height: 80px;
  }

  .productDetails {
    grid-area: details;
  }

  .quantitySection {
    grid-area: quantity;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    min-width: auto;
  }

  .quantityLabel {
    margin-right: 0.5rem;
  }

  .priceSection {
    grid-area: price;
    align-items: flex-start;
    min-width: auto;
  }

  .removeSection {
    grid-area: remove;
    justify-content: center;
    margin-top: 0.5rem;
  }

  .productName {
    font-size: 1.1rem;
  }

  .itemTotal {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .cartItem {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "image"
      "details"
      "quantity"
      "price"
      "remove";
    text-align: center;
  }

  .imageContainer {
    width: 120px;
    height: 120px;
    margin: 0 auto;
  }

  .quantitySection {
    justify-content: center;
  }

  .priceSection {
    align-items: center;
  }
}
