/* Contact Page Styles - NORM Architects Inspired Design */


.CSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 60px 20px;
  background-color: #fdfaf4; /* soft off-white background */
  font-family: 'Georgia', serif;
  color: #222;
}

.CBlock {
  margin-bottom: 40px;
  max-width: 600px;
}

.CHeader {
  font-size: 14px;
  letter-spacing: 4px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 10px;
}

.DHeader {
  font-size: 10px;
  letter-spacing: 4px;
  text-transform: uppercase;
  margin-bottom: 10px;
  align-content: left;
}

.CInfo {
  font-size: 14px;
  margin: 4px 0;
  line-height: 1.6;
}

.separator {
  border: none;
  border-top: 1px solid #ccc;
  width: 30px;
  margin: 20px auto 0;
}





.contactPage {
  min-height: 100vh;
  background: white;
  padding-top: 80px;
}

.contactContainer {
  display: flex;
  min-height: calc(100vh - 80px);
}

/* Left Section */
.leftSection {
  flex: 1;
  background: white;
  padding: 60px 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 50%;
}

.brandHeader {
  margin-bottom: 60px;
}

.brandName {
  font-size: 1.2rem;
  font-weight: 400;
  color: #333;
  letter-spacing: 2px;
  margin: 0;
}

/* Animated Text Styles */
.animatedTextContainer {
  margin-bottom: 40px;
  padding: 20px 0;
}

.animatedText {
  font-size: 2.5rem;
  font-weight: 300;
  color: #333;
  line-height: 1.2;
  margin: 0;
  overflow: hidden;
}

.word {
  text-content: center;
  display: inline-block;
  margin-right: 0.3em;
  opacity: 0;
  transform: translateY(100%);
  animation: slideInUp 0.8s ease-out forwards;
  font-family: 'Georgia', serif;

}

.word:nth-child(1) { animation-delay: 0ms; }
.word:nth-child(2) { animation-delay: 200ms; }
.word:nth-child(3) { animation-delay: 400ms; }
.word:nth-child(4) { animation-delay: 600ms; }
.word:nth-child(5) { animation-delay: 800ms; }

/* Left Panel Form Container */
.leftFormContainer {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 180px);
  padding-right: 10px;
}

.leftFormGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  max-width: 100%;
}

/* Form styling for left panel */
.leftFormContainer .formHeader {
  text-align: left;
  margin-bottom: 20px;
}

.leftFormContainer .formTitle {
  font-size: 1.5rem;
  font-weight: 300;
  color: #2c2c2c;
  margin-bottom: 8px;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.leftFormContainer .formSubtitle {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
  margin: 0;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.leftFormContainer .contactForm {
  background: transparent;
  padding: 0;
  box-shadow: none;
}

/* Compact form group styling */
.leftFormContainer .formGroup,
.leftFormContainer .formGroupFull {
  margin-bottom: 0;
}

.leftFormContainer .formGroupFull {
  grid-column: 1 / -1;
}

.leftFormContainer .input,
.leftFormContainer .select,
.leftFormContainer .textarea {
  padding: 10px 12px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 0.9rem;
  background: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 42px;
}

/* Border drawing animation container */
.leftFormContainer .input::before,
.leftFormContainer .select::before,
.leftFormContainer .textarea::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid #0066cc;
  border-radius: 4px;
  opacity: 0;
  animation: borderDraw 2s ease-out forwards;
  animation-delay: inherit;
}

/* Individual border segments */
.leftFormContainer .input::after,
.leftFormContainer .select::after,
.leftFormContainer .textarea::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent;
  border-radius: 4px;
  background:
    linear-gradient(90deg, #0066cc 50%, transparent 50%) bottom left / 0% 2px no-repeat,
    linear-gradient(0deg, #0066cc 50%, transparent 50%) bottom right / 2px 0% no-repeat,
    linear-gradient(-90deg, #0066cc 50%, transparent 50%) top right / 0% 2px no-repeat,
    linear-gradient(180deg, #0066cc 50%, transparent 50%) top left / 2px 0% no-repeat;
  animation: drawBorderClockwise 2s ease-out forwards;
  animation-delay: inherit;
}

.leftFormContainer .input:focus,
.leftFormContainer .select:focus,
.leftFormContainer .textarea:focus {
  border-color: #0066cc;
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
}

.leftFormContainer .textarea {
  min-height: 80px;
  height: auto;
  resize: vertical;
}

.leftFormContainer .submitButton {
  background: #fdfaf4;
  color: black;
  border: 1px solid transparent;
  padding: 12px 25px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 4px;
  width: 100%;
  max-width: none;
  margin: 0;
  position: relative;
  overflow: hidden;
  height: 42px;
  margin-bottom: 10px;
  font-family: 'Georgia', serif;

}



/* Submit button border animation */
.leftFormContainer .submitButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid #ffffff;
  border-radius: 4px;
  opacity: 0;
  animation: borderDraw 2s ease-out forwards;
  animation-delay: inherit;
}

.leftFormContainer .submitButton::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent;
  border-radius: 4px;
  background:
    linear-gradient(90deg, #ffffff 50%, transparent 50%) bottom left / 0% 2px no-repeat,
    linear-gradient(0deg, #ffffff 50%, transparent 50%) bottom right / 2px 0% no-repeat,
    linear-gradient(-90deg, #ffffff 50%, transparent 50%) top right / 0% 2px no-repeat,
    linear-gradient(180deg, #ffffff 50%, transparent 50%) top left / 2px 0% no-repeat;
  animation: drawBorderClockwise 2s ease-out forwards;
  animation-delay: inherit;
}

.leftFormContainer .submitButton:hover:not(:disabled) {
  background: #e7c58bff;
  transform: translateY(-1px);
}

.leftFormContainer .label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c2c2c;
  margin-bottom: 4px;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* Right Section */
.rightSection {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.imageContainer {
  width: 100%;
  height: 100%;
  position: relative;
}

.contactImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Form Section */
.formSection {
  background: #f8f9fa;
  padding: 80px 0;
}

.formContainer {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 40px;
}

.formHeader {
  text-align: center;
  margin-bottom: 50px;
}

.formTitle {
  font-size: 2.2rem;
  font-weight: 300;
  color: #2c2c2c;
  margin-bottom: 15px;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.formSubtitle {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.contactForm {
  background: white;
  padding: 50px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-bottom: 30px;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroupFull {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
}



.label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c2c2c;
  margin-bottom: 8px;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.input,
.select,
.textarea {
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 1rem;
  color: #2c2c2c;
  background: white;
  transition: all 0.3s ease;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #0066cc;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.input::placeholder,
.textarea::placeholder {
  color: #999;
}

.select {
  cursor: pointer;
  appearance: none;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23666" stroke-width="2"><polyline points="6,9 12,15 18,9"/></svg>');
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 16px;
}

.textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

.submitButton {
  background: linear-gradient(135deg, #0066cc, #004499);
  color: white;
  border: none;
  padding: 18px 40px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  max-width: 200px;
  margin: 0 auto;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 102, 204, 0.3);
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submitting {
  background: #999;
}

.submitMessage {
  padding: 15px 20px;
  border-radius: 6px;
  margin-bottom: 30px;
  text-align: center;
  font-weight: 500;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.submitMessage.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.submitMessage.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Animation Styles */
.slideInUp {
  animation: slideInUp 0.8s ease-out forwards;
}

.fadeIn {
  animation: fadeIn 0.8s ease-out forwards;
}

.slideInLeft {
  animation: slideInLeft 0.8s ease-out forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes yodezeenSlideUp {
  0% {
    opacity: 0;
    transform: translateY(60px);
  }
  50% {
    opacity: 0.3;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Border drawing animation - clockwise from bottom-left */
@keyframes drawBorderClockwise {
  0% {
    background-size: 0% 2px, 2px 0%, 0% 2px, 2px 0%;
  }
  25% {
    background-size: 100% 2px, 2px 0%, 0% 2px, 2px 0%;
  }
  50% {
    background-size: 100% 2px, 2px 100%, 0% 2px, 2px 0%;
  }
  75% {
    background-size: 100% 2px, 2px 100%, 100% 2px, 2px 0%;
  }
  100% {
    background-size: 100% 2px, 2px 100%, 100% 2px, 2px 100%;
  }
}

/* Fallback border animation for input fields */
@keyframes borderDraw {
  0% {
    opacity: 0;
    border-color: transparent;
  }
  100% {
    opacity: 1;
    border-color: #0066cc;
  }
}

/* Fallback border animation for submit button */
.leftFormContainer .submitButton::before {
  border-color: #ffffff !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contactContainer {
    flex-direction: column;
  }

  .leftSection {
    max-width: 100%;
    padding: 40px 30px;
  }

  .leftFormContainer {
    max-height: none;
  }

  .leftFormContainer .formTitle {
    font-size: 1.5rem;
  }

  .rightSection {
    min-height: 300px;
  }

  .brandName {
    font-size: 1rem;
    letter-spacing: 1.5px;
  }

  .animatedText {
    font-size: 2rem;
  }
    .animatedSubText {
    font-size: 1rem;
  }

  .formSection {
    padding: 60px 0;
  }

  .formContainer {
    padding: 0 30px;
  }

  .contactForm {
    padding: 40px 30px;
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .formTitle {
    font-size: 1.8rem;
  }

  .sectionTitle {
    font-size: 0.8rem;
    letter-spacing: 1.5px;
  }

  .contactEmail,
  .contactPhone {
    font-size: 0.9rem;
  }

  .locationInfo {
    font-size: 0.9rem;
  }

  .footerNote p {
    font-size: 0.8rem;
  }
   .leftFormGrid {
    grid-template-columns: 1fr !important; /* Single column layout */
  }

  .formGroup,
  .formGroupFull {
    grid-column: 1 / -1; /* Full width for all fields */
  }

  .submitButton {
    width: 100%;
  }

  .leftSection {
    padding: 30px 20px !important;
  }
}

@media (max-width: 480px) {
  .leftSection {
    padding: 30px 20px;
  }

  .leftFormContainer .formTitle {
    font-size: 1.3rem;
  }

  .leftFormContainer .input,
  .leftFormContainer .select,
  .leftFormContainer .textarea {
    padding: 10px 12px;
    font-size: 0.9rem;
  }

  /* Reduce border animation intensity on mobile */
  .leftFormContainer .input::after,
  .leftFormContainer .select::after,
  .leftFormContainer .textarea::after,
  .leftFormContainer .submitButton::after {
    animation-duration: 1.5s;
  }

  .leftFormContainer .submitButton {
    padding: 12px 25px;
    font-size: 0.9rem;
  }

  .contactSection {
    margin-bottom: 35px;
  }

  .brandHeader {
    margin-bottom: 40px;
  }

  .animatedText {
    font-size: 1.5rem;
  }

  .animatedTextContainer {
    margin-bottom: 30px;
  }

  .animatedSubTextContainer {
    margin-bottom: 4px;
  }

  .belowPadder{
  margin-top: 40px;
  }

  .formContainer {
    padding: 0 20px;
  }

  .contactForm {
    padding: 30px 20px;
  }

  .formTitle {
    font-size: 1.5rem;
  }

  .input,
  .select,
  .textarea {
    padding: 12px;
    font-size: 0.95rem;
  }

  .submitButton {
    padding: 15px 30px;
    font-size: 0.9rem;
  }
}

