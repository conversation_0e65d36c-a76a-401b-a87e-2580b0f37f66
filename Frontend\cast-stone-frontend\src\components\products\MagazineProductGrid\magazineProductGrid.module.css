/* Magazine Product Grid */
.productGrid {
  width: 100%;
}

/* Loading States */
.loadingContainer {
  width: 100%;
}

.loadingCard {
  background: #ffffff;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loadingImage {
  width: 100%;
  height: 250px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.loadingContent {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.loadingBadge {
  width: 80px;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 10px;
}

.loadingTitle {
  width: 70%;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

.loadingDescription {
  width: 100%;
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

.loadingPrice {
  width: 50%;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

.loadingButton {
  width: 100%;
  height: 40px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-top: auto;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Empty State */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6b7280;
}

.emptyIcon {
  width: 80px;
  height: 80px;
  margin-bottom: 2rem;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emptyIcon svg {
  width: 40px;
  height: 40px;
  stroke: #9ca3af;
  stroke-width: 2;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.emptyMessage {
  font-size: 1rem;
  line-height: 1.6;
  max-width: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .loadingContent {
    padding: 1.5rem;
  }
  
  .loadingImage {
    height: 200px;
  }
  
  .emptyContainer {
    padding: 3rem 1rem;
  }
  
  .emptyIcon {
    width: 60px;
    height: 60px;
  }
  
  .emptyIcon svg {
    width: 30px;
    height: 30px;
  }
  
  .emptyTitle {
    font-size: 1.25rem;
  }
  
  .emptyMessage {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .loadingContent {
    padding: 1rem;
  }
  
  .loadingImage {
    height: 180px;
  }
  
  .emptyContainer {
    padding: 2rem 1rem;
  }
}
