/* Elegant Description Section Styles */
.descriptionSection {
  padding: 8rem 0;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 50%, #f8f9fa 100%);
  position: relative;
  overflow: hidden;
  margin-bottom: -8rem;
}

.descriptionSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 206, 84, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.content {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.titleContainer {
  margin-bottom: 3rem;
  position: relative;
}

.title {
  font-size: 3.5rem;
  font-weight: 250;
  color: #342883ff;
  margin-bottom: 1rem;
  letter-spacing: 0.02em;
  line-height: 1.2;
  text-transform: uppercase;
  font-family: 'Georgia', serif;
}

.titleUnderline {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #d4af37 0%, #f4d03f 100%);
  margin: 0 auto;
  border-radius: 2px;
  position: relative;
}

.titleUnderline::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: #342883ff;
  border-radius: 50%;
  box-shadow: 0 0 0 4px rgba(212, 175, 55, 0.2);
}

.descriptionContainer {
  margin-bottom: 4rem;
}

.descriptionText {
  font-size: 1.25rem;
  line-height: 1.8;
  color: #4a4a4a;
  margin-bottom: 2rem;
  text-align: justify;
  font-weight: 400;
  letter-spacing: 0.01em;
  position: relative;
}

.descriptionText:last-child {
  margin-bottom: 0;
}


.decorativeElements {
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}




.decorativeLine {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg, 
    transparent 0%, 
    rgba(45, 29, 120, 0.3) 20%, 
    rgba(34, 34, 142, 0.6) 50%, 
    rgba(42, 25, 138, 0.3) 80%, 
    transparent 100%
  );
  z-index: -1;
}

/* Tablet Styles */
@media (max-width: 1024px) {
  .descriptionSection {
    padding: 6rem 0;
  }

  .container {
    padding: 0 3rem;
  }

  .title {
    font-size: 3rem;
  }

  .descriptionText {
    font-size: 1.15rem;
    line-height: 1.7;
  }

  .titleContainer {
    margin-bottom: 2.5rem;
  }

  .descriptionContainer {
    margin-bottom: 3rem;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .descriptionSection {
    padding: 4rem 0;
  }

  .container {
    padding: 0 2rem;
  }

  .title {
    font-size: 2.5rem;
    line-height: 1.3;
  }

  .descriptionText {
    font-size: 1.1rem;
    line-height: 1.6;
    text-align: left;
    margin-bottom: 1.5rem;
  }

  .titleContainer {
    margin-bottom: 2rem;
  }

  .descriptionContainer {
    margin-bottom: 2.5rem;
  }

  .decorativeElements {
    height: 40px;
  }

  .decorativeCircle {
    width: 16px;
    height: 16px;
  }

  .decorativeCircle::before {
    width: 6px;
    height: 6px;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .descriptionSection {
    padding: 3rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .descriptionText {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.2rem;
  }

  .titleContainer {
    margin-bottom: 1.5rem;
  }

  .descriptionContainer {
    margin-bottom: 2rem;
  }

  .titleUnderline {
    width: 60px;
    height: 2px;
  }

  .titleUnderline::after {
    width: 8px;
    height: 8px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .title {
    color: #000000;
  }

  .descriptionText {
    color: #000000;
  }

  .titleUnderline,
  .decorativeCircle,
  .decorativeCircle::before {
    background: #000000;
    border-color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .content,
  .titleContainer,
  .descriptionText,
  .decorativeElements {
    animation: none;
    transition: none;
  }
}
