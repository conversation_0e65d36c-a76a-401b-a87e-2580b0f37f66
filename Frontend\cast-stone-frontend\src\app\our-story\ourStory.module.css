/* Our Story Page Styles - Blue and White Theme */
.OurStoryroot {
  --cast-stone-blue: #2563eb;
  --cast-stone-light-blue: #3b82f6;
  --cast-stone-blue-50: #eff6ff;
  --cast-stone-white: #ffffff;
  --cast-stone-dark-text: #1f2937;
  --cast-stone-gray-text: #4b5563;
  --cast-stone-shadow: rgba(37, 99, 235, 0.1);
  --cast-stone-shadow-hover: rgba(37, 99, 235, 0.15);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Page Container */
.storyPage {
  min-height: 100vh;
  background-color: var(--cast-stone-white);
}

/* Section 1: Hero Section */
.heroSection {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.bannerContainer {
  width: 100%;
  height: 100%;
  position: relative;
}

.bannerImage {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.heroContent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4rem 2rem;
  z-index: 2;
}

.heroTextContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  max-width: 600px;
  margin-left: 5%;
}

.heroTitle {
  font-size: 4.5rem;
  font-weight: 300;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  line-height: 1.1;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.heroSubtitle {
  font-size: 1.4rem;
  font-weight: 300;
  line-height: 1.6;
  color: white;
  opacity: 0.95;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  max-width: 500px;
}

.scrollArrow {
  align-self: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  margin-bottom: 2rem;
}

.scrollArrow:hover {
  transform: translateY(-5px);
}

.arrowIcon {
  width: 48px;
  height: 48px;
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: bounce 2s infinite;
}

.arrowText {
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  opacity: 0.9;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}



/* Section 2: Timeline Section */
.timelineSection {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6)),
              url('/images/catalog-banner-bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: white;
  padding: 4rem 0;
}

.timelineBackground {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  height: 100%;
}

.timelineHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.timelineTitle {
  font-size: 3rem;
  font-weight: 300;
  letter-spacing: 0.2em;
  margin-bottom: 3rem;
  color: white;
}

.yearNavigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.yearButton {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.7);
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.yearButton:hover {
  border-color: white;
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.yearButtonActive {
  border-color: white;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-bottom: 3px solid white;
}

.moreButton {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.7);
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.moreButton:hover {
  border-color: white;
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.timelineContentContainer {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

.timelineContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 500px;
}

.timelineImageContainer {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.timelineImage {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.timelineImage:hover {
  transform: scale(1.05);
}

.timelineTextContainer {
  padding: 2rem;
}

.timelineYear {
  font-size: 4rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  line-height: 1;
}

.timelineItemTitle {
  font-size: 2.5rem;
  font-weight: 300;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.timelineDescription {
  font-size: 1.1rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.timelineNavigation {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  display: flex;
  gap: 1rem;
}

.navButton {
  width: 50px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.navButton:hover:not(:disabled) {
  border-color: white;
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.navButton:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}







/* Parallax Background Container */
.parallaxContainer {
  position: relative;
  background-image: url('/Black.jpg');
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  min-height: 300vh; /* Covers all three sections */
  width: 100%;
  overflow: hidden;
}

/* Overlay to darken background slightly for better text readability */
.parallaxContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* Letter Animation Styles */
.letterWrapper {
  perspective: 1200px;
  margin-bottom: 2rem;
}

.letterContainer {
  position: relative;
  transform-style: preserve-3d;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.letterFoldLine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 20%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.1) 80%,
    transparent 100%
  );
  z-index: 10;
  transform-origin: center;
}

.letterContent {
  position: relative;
  z-index: 1;
}

/* Section 3: Vision & Innovation Section */
.visionSection {
  background: transparent;
  padding: 6rem 0;
  min-height: 100vh;
  position: relative;
  z-index: 3;
}

.visionContainer {
  width: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 8rem;
}

.blogItem {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 500px;
  background: transparent;
  padding: 4rem 6rem;
  margin: 0;
  position: relative;
  z-index: 4;
  border-radius: 0;
  width: 100%;
}

.blogItemReverse {
  grid-template-columns: 1fr 1fr;
}

.blogImageContainer {
  position: relative;
  overflow: hidden;
  border-radius: 0;
  height: 500px;
}

.blogImage {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.blogImage:hover {
  transform: scale(1.02);
}

.blogContent {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.blogTitle {
  font-size: 2.5rem;
  font-weight: 300;
  color: #1a1a1a;
  margin-bottom: 2rem;
  line-height: 1.2;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.blogText {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #4a4a4a;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.blogText:last-child {
  margin-bottom: 0;
}

/* Quote Section */
.quoteSection {
  background: rgba(26, 26, 26, 0.95);
  padding: 6rem 0;
  margin: 0;
  width: 100%;
  position: relative;
  z-index: 4;
  backdrop-filter: blur(2px);
  border-radius: 0;
}

.quoteContainer {
  width: 100%;
  margin: 0;
  padding: 0 6rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
  min-height: 500px;
}

.quoteContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.quote {
  font-size: 1.3rem;
  line-height: 1.6;
  color: #ffffff;
  margin: 0 0 2rem 0;
  font-style: italic;
  font-weight: 300;
  text-align: left;
}

.quoteAuthor {
  font-size: 1rem;
  color: #00bcd4;
  font-weight: 600;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-style: normal;
}

.quoteTextContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.quoteTitle {
  font-size: 2.5rem;
  font-weight: 300;
  color: #ffffff;
  margin-bottom: 2rem;
  line-height: 1.2;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.quoteText {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #cccccc;
  text-align: justify;
}
/* Responsive Design */
@media (max-width: 1024px) {
  .heroTitle {
    font-size: 3.5rem;
  }

  .heroSubtitle {
    font-size: 1.2rem;
  }

  .heroTextContainer {
    margin-left: 3%;
  }

  .timelineContent {
    gap: 2rem;
  }

  .timelineTitle {
    font-size: 2.5rem;
  }

  .timelineItemTitle {
    font-size: 2rem;
  }

  .timelineYear {
    font-size: 3rem;
  }

  .visionContainer {
    gap: 6rem;
  }

  .letterContainer {
    border-radius: 8px;
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.08),
      0 1px 6px rgba(0, 0, 0, 0.04);
  }

  .blogItem {
    gap: 3rem;
    min-height: 400px;
    padding: 3rem 4rem;
  }

  .blogImageContainer {
    height: 400px;
  }

  .blogTitle {
    font-size: 2rem;
  }

  .blogText {
    font-size: 1rem;
  }

  .quoteContainer {
    gap: 4rem;
  }

  .quoteTitle {
    font-size: 2rem;
  }

  .quote {
    font-size: 1.2rem;
  }

  .quoteText {
    font-size: 1rem;
  }

  /* Disable parallax on smaller screens for better performance */
  .parallaxContainer {
    background-attachment: scroll;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1rem;
  }

  .heroTextContainer {
    margin-left: 0;
    align-items: center;
    text-align: center;
  }

  .heroContent {
    padding: 2rem 1rem;
  }

  .timelineBackground {
    padding: 0 1rem;
  }

  .timelineTitle {
    font-size: 2rem;
  }

  .yearNavigation {
    gap: 1rem;
    overflow-x: auto;
    padding-bottom: 1rem;
    justify-content: flex-start;
  }

  .yearButton {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    flex-shrink: 0;
  }

  .timelineContent {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .timelineImage {
    height: 300px;
  }

  .timelineYear {
    font-size: 2.5rem;
  }

  .timelineItemTitle {
    font-size: 1.8rem;
  }

  .timelineDescription {
    font-size: 1rem;
  }

  .timelineNavigation {
    position: static;
    justify-content: center;
    margin-top: 2rem;
  }

  .visionSection {
    padding: 4rem 0;
  }

  .visionContainer {
    gap: 4rem;
    padding: 0;
  }

  .letterWrapper {
    perspective: 800px;
  }

  .letterContainer {
    border-radius: 6px;
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.06),
      0 1px 4px rgba(0, 0, 0, 0.03);
  }

  .blogItem,
  .blogItemReverse {
    grid-template-columns: 1fr;
    gap: 2rem;
    min-height: auto;
    text-align: center;
    padding: 3rem 2rem;
  }

  .blogImageContainer {
    height: 300px;
    order: 1;
  }

  .blogContent {
    padding: 1rem;
    order: 2;
  }

  .blogTitle {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .blogText {
    font-size: 1rem;
    text-align: left;
  }

  .quoteSection {
    padding: 4rem 0;
  }

  .quoteContainer {
    grid-template-columns: 1fr;
    gap: 3rem;
    padding: 0 2rem;
    text-align: center;
  }

  .quoteTitle {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .quote {
    font-size: 1.1rem;
    text-align: center;
  }

  .quoteText {
    text-align: left;
  }

  /* Disable parallax on tablets */
  .parallaxContainer {
    background-attachment: scroll;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 0.9rem;
  }

  .timelineTitle {
    font-size: 1.5rem;
  }

  .yearButton {
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
  }

  .timelineYear {
    font-size: 2rem;
  }

  .timelineItemTitle {
    font-size: 1.5rem;
  }

  .timelineDescription {
    font-size: 0.9rem;
  }

  .timelineImage {
    height: 250px;
  }

  .navButton {
    width: 40px;
    height: 40px;
  }

  .visionSection {
    padding: 3rem 0;
  }

  .visionContainer {
    gap: 3rem;
    padding: 0;
  }

  .letterWrapper {
    perspective: 600px;
  }

  .letterContainer {
    border-radius: 4px;
    box-shadow:
      0 4px 15px rgba(0, 0, 0, 0.05),
      0 1px 3px rgba(0, 0, 0, 0.02);
  }

  .blogItem,
  .blogItemReverse {
    padding: 2rem 1rem;
  }

  .blogTitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .blogText {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .blogImageContainer {
    height: 250px;
  }

  .quoteSection {
    padding: 3rem 0;
  }

  .quoteContainer {
    gap: 2rem;
    padding: 0 1rem;
  }

  .quoteTitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .quote {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .quoteText {
    font-size: 0.9rem;
  }

  /* Disable parallax on mobile */
  .parallaxContainer {
    background-attachment: scroll;
  }
}
