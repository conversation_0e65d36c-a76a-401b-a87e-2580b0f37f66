/* Static Completed Projects Styles */
.projectsSection {
  padding: 8rem 0;
  background: linear-gradient(135deg, #c7c5c5ff 0%, #ffffffff 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-size: 3rem;
  font-weight: 300;
  margin-bottom: 1rem;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: #000000ff;
}

.subtitle {
  font-size: 1.2rem;
  color: #000000ff;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.projectsList {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.projectRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.projectRow:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.projectImages {
  display: grid;
  gap: 1rem;
  height: 400px;
}

/* Single image layout - fallback for browsers that don't support :has() */
.projectImages {
  grid-template-columns: 1fr 1fr;
}

/* Single image layout */
.projectImages:has(.singleImage) {
  grid-template-columns: 1fr;
}

/* Two images layout */
.projectImages:not(:has(.singleImage)) {
  grid-template-columns: 1fr 1fr;
}

.imageContainer {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
}

.singleImage {
  grid-column: 1 / -1;
}

.projectImage {
  transition: transform 0.3s ease;
}

.projectRow:hover .projectImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.3) 100%
  );
}

.projectInfo {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.projectTitle {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000000ff;
  line-height: 1.3;
}

.projectDescription {
  font-size: 1.1rem;
  color: #000000ff;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.projectMeta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metaItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  padding: 0.3rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.metaItem:last-child {
  border-bottom: none;
}

.metaLabel {
  color: #000000ff;
  font-weight: 500;
}

.metaValue {
  color: #000000ff;
  font-weight: 400;
}

.emptyState {
  text-align: center;
  padding: 4rem 2rem;
}

.emptyMessage {
  font-size: 1.1rem;
  color: #cccccc;
  font-style: italic;
}

/* Tablet Styles */
@media (max-width: 1024px) {
  .projectsSection {
    padding: 6rem 0;
  }

  .title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .projectRow {
    gap: 2rem;
    padding: 1.5rem;
  }

  .projectImages {
    height: 350px;
  }

  .projectTitle {
    font-size: 1.8rem;
  }

  .projectDescription {
    font-size: 1rem;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .projectsSection {
    padding: 4rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .header {
    margin-bottom: 3rem;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .projectsList {
    gap: 3rem;
  }

  .projectRow {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .projectImages {
    height: 250px;
    order: 1;
  }

  .projectImages:not(:has(.singleImage)) {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 0.5rem;
  }

  .projectInfo {
    padding: 0.5rem;
    order: 2;
  }

  .projectTitle {
    font-size: 1.5rem;
  }

  .projectDescription {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .metaItem {
    font-size: 0.8rem;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .projectRow {
    padding: 1rem;
  }

  .projectImages {
    height: 200px;
  }

  .projectTitle {
    font-size: 1.3rem;
  }

  .projectDescription {
    font-size: 0.85rem;
  }
}


