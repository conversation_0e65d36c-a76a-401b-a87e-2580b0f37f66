/* Placeholder Image Component */
.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  color: #6b7280;
  transition: all 0.3s ease;
}

.placeholder:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  text-align: center;
  padding: 1rem;
}

.icon {
  width: 48px;
  height: 48px;
  stroke-width: 1.5;
  opacity: 0.6;
}

.text {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.8;
}

/* Type Variants */
.product {
  background: #fef3f2;
  border-color: #fecaca;
  color: #dc2626;
}

.product:hover {
  background: #fee2e2;
  border-color: #f87171;
}

.collection {
  background: #f0f9ff;
  border-color: #bae6fd;
  color: #0284c7;
}

.collection:hover {
  background: #e0f2fe;
  border-color: #7dd3fc;
}

.general {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #6b7280;
}

.general:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .icon {
    width: 32px;
    height: 32px;
  }
  
  .text {
    font-size: 0.75rem;
  }
  
  .content {
    gap: 0.5rem;
    padding: 0.75rem;
  }
}
