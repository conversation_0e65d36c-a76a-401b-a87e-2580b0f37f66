.collectionsCollage {
  padding: 6rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #faf9f7 100%);
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 1rem;
}

.subtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  color: #1e40af;
  max-width: 600px;
  margin: 0 auto;
}

/* Dynamic Collage Grid */
.collageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-auto-rows: 200px;
  gap: 1rem;
}

/* Collection Cards */
.collectionCard {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: transparent;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  grid-row: span 1;
}

.collectionCard.large {
  grid-column: span 2;
  grid-row: span 2;
}

.collectionCard.medium {
  grid-column: span 2;
  grid-row: span 1;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.collectionImage {
  object-fit: cover;
  object-position: center;
  transition: transform 0.6s ease;
}

.collectionCard:hover .collectionImage {
  transform: scale(1.05);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.cardContent {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  color: white;
  z-index: 2;
}

.collectionName {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.large .collectionName {
  font-size: 2rem;
}

.medium .collectionName {
  font-size: 1.75rem;
}

.exploreButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.collectionCard:hover .exploreButton {
  opacity: 1;
  transform: translateY(0);
}

.arrow {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.collectionCard:hover .arrow {
  transform: translateX(4px);
}

/* Responsive */
@media (max-width: 1024px) {
  .collectionCard.large,
  .collectionCard.medium {
    grid-column: span 1;
    grid-row: span 1;
  }
}
