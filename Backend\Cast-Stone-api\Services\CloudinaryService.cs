﻿using CloudinaryDotNet;
using CloudinaryDotNet.Actions;

public class CloudinaryService
{
    private readonly Cloudinary _cloudinary;

    public CloudinaryService(IConfiguration config)
    {
        var account = new Account(
        config["Cloudinary:CloudName"],
        config["Cloudinary:ApiKey"],
        config["Cloudinary:ApiSecret"]
        );
        _cloudinary = new Cloudinary(account);
    }

    public async Task<string> UploadImageAsync(IFormFile file)
    {
        await using var stream = file.OpenReadStream();
        var uploadParams = new ImageUploadParams
        {
            // File = new FileDescription(file.FileName, stream),
            // PublicId = Guid.NewGuid().ToString(), // Unique ID
            // Folder = "cast-stone-images" // Unified folder for all images

        File = new FileDescription(file.FileName, stream),
        Folder = "cast-stone-images",
        UseFilename = true,            
        UniqueFilename = false,        
        Overwrite = false  
        };

        var uploadResult = await _cloudinary.UploadAsync(uploadParams);
        return uploadResult.SecureUrl.ToString();
    }

    public async Task<List<CloudinaryImageInfo>> GetAllImagesAsync()
    {
        var listParams = new ListResourcesParams
        {
            Type = "upload",
            MaxResults = 500
        };

        var listResult = await _cloudinary.ListResourcesAsync(listParams);

        // Filter by folder prefix and convert to our model
        return listResult.Resources
            .Where(resource => resource.PublicId.StartsWith("cast-stone-images/"))
            .Select(resource => new CloudinaryImageInfo
            {
                PublicId = resource.PublicId,
                SecureUrl = resource.SecureUrl.ToString(),
                FileName = resource.PublicId.Split('/').LastOrDefault() ?? resource.PublicId,
                CreatedAt = DateTime.TryParse(resource.CreatedAt, out var date) ? date : DateTime.UtcNow
            }).ToList();
    }

    public async Task<bool> DeleteImageAsync(string publicId)
    {
        var deleteParams = new DeletionParams(publicId);
        var deleteResult = await _cloudinary.DestroyAsync(deleteParams);

        return deleteResult.Result == "ok";
    }

    public async Task<List<BulkUploadResult>> UploadImagesAsync(IFormFileCollection files)
    {
        var results = new List<BulkUploadResult>();

        foreach (var file in files)
        {
            try
            {
                await using var stream = file.OpenReadStream();
                var uploadParams = new ImageUploadParams
                {
                    File = new FileDescription(file.FileName, stream),
                    Folder = "cast-stone-images", // Unified folder for all images
                    UseFilename = true,            
                    UniqueFilename = false,        
                    Overwrite = false  

                };

                var uploadResult = await _cloudinary.UploadAsync(uploadParams);

                results.Add(new BulkUploadResult
                {
                    FileName = file.FileName,
                    SecureUrl = uploadResult.SecureUrl.ToString(),
                    PublicId = uploadResult.PublicId,
                    Success = true,
                    ErrorMessage = null
                });
            }
            catch (Exception ex)
            {
                results.Add(new BulkUploadResult
                {
                    FileName = file.FileName,
                    SecureUrl = null,
                    PublicId = null,
                    Success = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        return results;
    }
}

public class CloudinaryImageInfo
{
    public string PublicId { get; set; } = string.Empty;
    public string SecureUrl { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class BulkUploadResult
{
    public string FileName { get; set; } = string.Empty;
    public string? SecureUrl { get; set; }
    public string? PublicId { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}
