/* Magazine-Style Catalog Page */
.container {
  min-height: 100vh;
  background: #ffffff;
  color: #1f2937;
  padding-top: 4.2rem;
}

/* Hero Section Styling */
.heroSection {
  background: #fafafa;
}

/* Catalog Navigation Section */
.catalogNavigation {
  padding: 4rem 0;
  background: #ffffff;
}

.navigationContainer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.navigationHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.navigationTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.navigationSubtitle {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.navigationGrid {
  margin-top: 3rem;
}

/* Features Section Styling */
.featuresSection {
  background: #f9fafb;
}

.featuresList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.featureItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.featureIcon {
  width: 48px;
  height: 48px;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.featureIcon svg {
  width: 24px;
  height: 24px;
  stroke: #374151;
  stroke-width: 2;
}

.featureItem h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.featureItem p {
  font-size: 0.95rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* CTA Section Styling */
.ctaSection {
  background: #1f2937;
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .navigationTitle {
    font-size: 2.25rem;
  }

  .featuresList {
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding-top: 3rem;
  }

  .catalogNavigation {
    padding: 3rem 0;
  }

  .navigationContainer {
    padding: 0 1rem;
  }

  .navigationTitle {
    font-size: 2rem;
  }

  .navigationSubtitle {
    font-size: 1rem;
  }

  .featuresList {
    gap: 1rem;
  }

  .featureItem {
    padding: 1.25rem;
  }

  .featureIcon {
    width: 40px;
    height: 40px;
  }

  .featureIcon svg {
    width: 20px;
    height: 20px;
  }

  .featureItem h4 {
    font-size: 1rem;
  }

  .featureItem p {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .catalogNavigation {
    padding: 2rem 0;
  }

  .navigationTitle {
    font-size: 1.75rem;
  }

  .navigationSubtitle {
    font-size: 0.95rem;
  }

  .featureItem {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }

  .featureIcon {
    margin: 0 auto 1rem;
  }
}
