/* Categories Section Styles */
.categoriesSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  /* height: 100% */
}

/* Header Styles */
.header {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.sectionTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.sectionSubtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  color:rgb(54, 76, 137);
  line-height: 1.6;
  font-weight: 400;
}

/* Grid Layout */
.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Loading States */
.loadingGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.loadingCard {
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.08);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loadingImage {
  width: 100%;
  height: 300px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.loadingContent {
  padding: 2rem;
}

.loadingText {
  height: 1rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}

.loadingText:nth-child(1) {
  width: 60%;
}

.loadingText:nth-child(2) {
  width: 80%;
}

.loadingText:nth-child(3) {
  width: 40%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Error State */
.errorMessage {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
  font-size: 1.125rem;
}

/* Category Card Styles */
.categoryCard {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 8px 32px rgba(37, 99, 235, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 600px;
}

.categoryCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(37, 99, 235, 0.15);
}

.cardLink {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
  position: relative;
}

/* Image Styles */
.imageContainer {
  position: relative;
  height: 60%;
  overflow: hidden;
}

.categoryImage {
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.categoryCard:hover .categoryImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
rgba(25, 35, 65, 0.4) 0%,
    rgba(20, 30, 60, 0.35) 50%,
    rgba(30, 40, 70, 0.4) 100%
  );
  z-index: 1;
}

.noImagePlaceholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.noImageText {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Card Content */
.cardContent {
  position: relative;
  height: 40%;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.cardHeader {
  margin-bottom: 1rem;
}

.stats {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.statsNumber {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #1e3a8a;
  line-height: 1;
}

.statsLabel {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.75rem;
  font-weight: 500;
  color: #1e3a8a;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.categoryTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e3a8a;
  margin: 0.25rem 0;
  line-height: 1.2;
}

.categorySubtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  color:rgb(48, 69, 126);
  text-transform: uppercase;
  letter-spacing: 0.15em;
  margin: 0;
}

.categoryDescription {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: #1e3a8a;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex-grow: 1;
}

/* Card Actions */
.cardActions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.actionButton,
.secondaryButton {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.actionButton {
  background: #1e3a8a;
  color: #ffffff;
  padding: 0.5rem 1rem;
}

.actionButton:hover {
  background: #1e3a8a;
  transform: translateY(-1px);
}

.secondaryButton {
  background: transparent;
  color:rgb(51, 73, 134);
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(105, 120, 161, 0.3);
}

.secondaryButton:hover {
  background: rgba(69, 66, 153, 0.1);
  color: #1e3a8a;
}

/* Hover Effect */
.hoverEffect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(37, 99, 235, 0.05) 0%,
    transparent 50%,
    rgba(29, 78, 216, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.categoryCard:hover .hoverEffect {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .categoriesSection {
    padding: 4rem 0;
  }
  
  .container {
    padding: 0 1.5rem;
  }
  
  .sectionTitle {
    font-size: 2.5rem;
  }
  
  .grid {
    gap: 1.5rem;
  }
  
  .categoryCard {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 2rem; /* Increased gap between cards */
  }

  .categoryCard {
    height: 380px; /* Increased height for better mobile experience */
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionSubtitle {
    font-size: 1rem;
  }

  .cardContent {
    padding: 1.25rem;
  }

  .statsNumber {
    font-size: 1.75rem;
  }

  .categoryTitle {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .categoriesSection {
    padding: 3rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .header {
    margin-bottom: 2.5rem;
  }

  .grid {
    gap: 1.5rem; /* Better gap for small screens */
  }

  .categoryCard {
    height: 360px; /* Increased height for better mobile experience */
  }

  .cardContent {
    padding: 1rem;
  }

  .cardActions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .actionButton,
  .secondaryButton {
    width: 100%;
    justify-content: center;
  }
}
