/* Checkout Page Styles - Magazine/Editorial Theme */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 8rem 1rem 2rem;
  min-height: 80vh;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #f3f4f6;
}

.title {
  color: #1f2937;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  line-height: 1.2;
}

/* Step Indicator */
.stepIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.step span {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step.active span {
  background: #2563eb;
  color: white;
}

.step label {
  color: #4b5563;
  font-size: 0.9rem;
  font-weight: 600;
}

.step.active label {
  color: #1f2937;
}

.stepConnector {
  width: 60px;
  height: 2px;
  background: #e5e7eb;
}

/* Checkout Content */
.checkoutContent {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 3rem;
}

/* Main Content */
.mainContent {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.sectionTitle {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

/* Form Styles */
.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup.fullWidth {
  grid-column: 1 / -1;
}

.formGroup label {
  color: #1f2937;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.formGroup input,
.formGroup select {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.formGroup input:focus,
.formGroup select:focus {
  outline: none;
  border-color: #2563eb;
}

/* Payment Methods */
.paymentMethods {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.paymentMethod {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paymentMethod:hover {
  border-color: #2563eb;
}

.paymentMethod.selected {
  border-color: #2563eb;
  background: #eff6ff;
}

.paymentIcon {
  font-size: 2rem;
  width: 60px;
  text-align: center;
}

.paymentInfo {
  flex: 1;
}

.paymentInfo h3 {
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.paymentInfo p {
  color: #4b5563;
  font-size: 0.9rem;
  margin: 0;
}

.radioButton input {
  width: 20px;
  height: 20px;
  accent-color: #2563eb;
}

/* Step Actions */
.stepActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.nextBtn,
.placeOrderBtn {
  padding: 1rem 2rem;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nextBtn:hover,
.placeOrderBtn:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-2px);
}

.placeOrderBtn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.backBtn {
  padding: 1rem 2rem;
  background: transparent;
  color: #4b5563;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backBtn:hover {
  border-color: #2563eb;
  color: #2563eb;
}

/* Order Summary */
.orderSummary {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.summaryTitle {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

/* Order Items */
.orderItems {
  margin-bottom: 1.5rem;
}

.orderItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.orderItem:last-child {
  border-bottom: none;
}

.itemImage {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
}

.itemDetails {
  flex: 1;
  min-width: 0;
}

.itemDetails h4 {
  color: #1f2937;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.itemDetails p {
  color: #4b5563;
  font-size: 0.8rem;
  margin: 0;
}

.itemPrice {
  color: #1f2937;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Summary Totals */
.summaryTotals {
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.summaryRow span:first-child {
  color: #4b5563;
  font-size: 0.9rem;
}

.summaryRow span:last-child {
  color: #1f2937;
  font-weight: 600;
  font-size: 0.9rem;
}

.totalRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-top: 2px solid #2563eb;
  margin-top: 1rem;
}

.totalRow span:first-child {
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 700;
}

.totalRow span:last-child {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 700;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .checkoutContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .orderSummary {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .title {
    font-size: 2rem;
  }

  .stepIndicator {
    gap: 0.5rem;
  }

  .step span {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .stepConnector {
    width: 40px;
  }

  .mainContent,
  .orderSummary {
    padding: 1.5rem;
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stepActions {
    flex-direction: column-reverse;
  }

  .nextBtn,
  .placeOrderBtn,
  .backBtn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 6rem 0.5rem 0.5rem;
  }

  .title {
    font-size: 1.75rem;
  }

  .mainContent,
  .orderSummary {
    padding: 1rem;
  }

  .sectionTitle {
    font-size: 1.25rem;
  }

  .paymentMethod {
    padding: 1rem;
  }

  .paymentIcon {
    font-size: 1.5rem;
    width: 40px;
  }

  .orderItem {
    flex-direction: column;
    align-items: flex-start;
    text-align: center;
  }

  .itemImage {
    align-self: center;
  }
}

/* Error message styles */
.errorMessage {
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 16px 0;
}

.errorMessage p {
  color: #dc2626;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/* Payment method improvements */
.paymentMethod {
  transition: all 0.2s ease;
  cursor: pointer;
}

.paymentMethod:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.paymentMethod.selected {
  border-color: #1e3a8a;
  background-color: #f0f4ff;
}

/* Processing state */
.placeOrderBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
