/* Completed Projects Carousel Styles */
.projectsCarousel {
  padding: 8rem 0;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-size: 3rem;
  font-weight: 300;
  margin-bottom: 1rem;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: #ffffff;
}

.subtitle {
  font-size: 1.2rem;
  color: #cccccc;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.swiperContainer {
  position: relative;
  padding: 2rem 0 4rem;
}

.swiper {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
}

.slide {
  background-position: center;
  background-size: cover;
  width: 400px;
  height: 500px;
}

.projectCard {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.projectCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 60%;
  overflow: hidden;
}

.projectImage {
  transition: transform 0.3s ease;
}

.projectCard:hover .projectImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.3) 100%
  );
}

.projectInfo {
  padding: 1.5rem;
  height: 40%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.projectTitle {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #ffffff;
  line-height: 1.3;
}

.projectDescription {
  font-size: 0.9rem;
  color: #cccccc;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex-grow: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.projectMeta {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.metaItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.metaLabel {
  color: #999999;
  font-weight: 500;
}

.metaValue {
  color: #ffffff;
  font-weight: 400;
}

.emptyState {
  text-align: center;
  padding: 4rem 2rem;
}

.emptyMessage {
  font-size: 1.1rem;
  color: #cccccc;
  font-style: italic;
}

/* Swiper Navigation Customization */
.swiper :global(.swiper-button-next),
.swiper :global(.swiper-button-prev) {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  margin-top: -25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.swiper :global(.swiper-button-next):hover,
.swiper :global(.swiper-button-prev):hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.swiper :global(.swiper-button-next::after),
.swiper :global(.swiper-button-prev::after) {
  font-size: 18px;
  font-weight: bold;
}

/* Swiper Pagination Customization */
.swiper :global(.swiper-pagination-bullet) {
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;
  transition: all 0.3s ease;
}

.swiper :global(.swiper-pagination-bullet-active) {
  background: #ffffff;
  transform: scale(1.2);
}

/* Tablet Styles */
@media (max-width: 1024px) {
  .projectsCarousel {
    padding: 6rem 0;
  }

  .title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .slide {
    width: 350px;
    height: 450px;
  }

  .projectTitle {
    font-size: 1.2rem;
  }

  .projectDescription {
    font-size: 0.85rem;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .projectsCarousel {
    padding: 4rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .header {
    margin-bottom: 3rem;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .slide {
    width: 300px;
    height: 400px;
  }

  .projectInfo {
    padding: 1rem;
  }

  .projectTitle {
    font-size: 1.1rem;
  }

  .projectDescription {
    font-size: 0.8rem;
    -webkit-line-clamp: 2;
  }

  .metaItem {
    font-size: 0.75rem;
  }

  .swiper :global(.swiper-button-next),
  .swiper :global(.swiper-button-prev) {
    width: 40px;
    height: 40px;
    margin-top: -20px;
  }

  .swiper :global(.swiper-button-next::after),
  .swiper :global(.swiper-button-prev::after) {
    font-size: 14px;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .slide {
    width: 280px;
    height: 380px;
  }

  .title {
    font-size: 1.8rem;
  }

  .projectInfo {
    padding: 0.8rem;
  }

  .projectTitle {
    font-size: 1rem;
  }
}
