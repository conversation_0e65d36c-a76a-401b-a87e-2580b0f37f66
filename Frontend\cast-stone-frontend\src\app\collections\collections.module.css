/* Magazine-Style Collections Page */
.container {
  min-height: 100vh;
  background: #ffffff;
  color: #1f2937;
  padding-top: 4.2rem;
}

/* Hero Section Styling */
.heroSection {
  background: #fafafa;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #1f2937;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1f2937;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  text-align: center;
  padding: 4rem 2rem;
  color: #1f2937;
}

.errorContainer h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #dc2626;
}

.retryButton {
  background: #1f2937;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
  transition: background 0.3s ease;
}

.retryButton:hover {
  background: #374151;
}

/* Collections Section */
.collectionsSection {
  padding: 4rem 0;
  background: #ffffff;
}

.collectionsContainer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.sectionSubtitle {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.collectionsGrid {
  margin-top: 3rem;
}

.collectionCard {
  transition: all 0.3s ease;
}

.collectionCard:hover {
  transform: translateY(-4px);
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.emptyIcon {
  width: 80px;
  height: 80px;
  margin: 0 auto 2rem;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emptyIcon svg {
  width: 40px;
  height: 40px;
  stroke: #9ca3af;
  stroke-width: 2;
}

.emptyState h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.emptyState p {
  font-size: 1rem;
  line-height: 1.6;
}

/* CTA Section Styling */
.ctaSection {
  background: #f9fafb;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sectionTitle {
    font-size: 2.25rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding-top: 3rem;
  }

  .collectionsSection {
    padding: 3rem 0;
  }

  .collectionsContainer {
    padding: 0 1rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionSubtitle {
    font-size: 1rem;
  }

  .emptyState {
    padding: 3rem 1rem;
  }

  .emptyIcon {
    width: 60px;
    height: 60px;
  }

  .emptyIcon svg {
    width: 30px;
    height: 30px;
  }
}

@media (max-width: 480px) {
  .collectionsSection {
    padding: 2rem 0;
  }

  .sectionTitle {
    font-size: 1.75rem;
  }

  .sectionSubtitle {
    font-size: 0.95rem;
  }

  .emptyState {
    padding: 2rem 1rem;
  }
}
