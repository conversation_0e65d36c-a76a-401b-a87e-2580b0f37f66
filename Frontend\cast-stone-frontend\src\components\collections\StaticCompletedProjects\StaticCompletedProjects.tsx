/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getOptimizedImageUrl } from '@/utils/cloudinaryUtils';
import { Collection } from '@/services/types/entities';
import styles from './StaticCompletedProjects.module.css';

export interface StaticCompletedProject {
  id: string;
  collectionId: number; // Collection ID this project belongs to
  title: string;
  description: string;
  images: string[]; // 1-2 images per project
  imageAlts: string[]; // Alt text for each image
  location?: string;
  completedDate?: string;
  projectType?: string;
  clientName?: string;
}

interface StaticCompletedProjectsProps {
  projects: StaticCompletedProject[];
  currentCollectionId: number;
  title?: string;
  subtitle?: string;
  className?: string;
}

interface YouMayAlsoLikeProps {
  collections: Collection[];
  currentCollectionId: number;
  title?: string;
  className?: string;
}

const StaticCompletedProjects: React.FC<StaticCompletedProjectsProps> = ({
  projects,
  currentCollectionId,
  title = "Featured Projects",
  subtitle = "Discover our stunning architectural stone installations",
  className = ''
}) => {
  // Filter projects by current collection ID
  const filteredProjects = projects.filter(project => project.collectionId === currentCollectionId);

  if (filteredProjects.length === 0) {
    return (
      <section className={`${styles.projectsSection} ${className}`}>
        <div className={styles.container}>
          <div className={styles.header}>
            <h2 className={styles.title}>{title}</h2>
            <p className={styles.subtitle}>{subtitle}</p>
          </div>
          <div className={styles.emptyState}>
            <p className={styles.emptyMessage}>No completed projects available for this collection.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`${styles.projectsSection} ${className}`}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>{title}</h2>
          <p className={styles.subtitle}>{subtitle}</p>
        </div>

        <div className={styles.projectsList}>
          {filteredProjects.map((project, index) => (
            <div key={project.id} className={styles.projectRow}>
              {/* Project Images */}
              <div className={styles.projectImages}>
                {project.images.slice(0, 2).map((imageSrc, imageIndex) => {
                  const optimizedImageSrc = getOptimizedImageUrl(imageSrc, 'hero');
                  const imageAlt = project.imageAlts[imageIndex] || `${project.title} - Image ${imageIndex + 1}`;
                  
                  return (
                    <div 
                      key={imageIndex} 
                      className={`${styles.imageContainer} ${project.images.length === 1 ? styles.singleImage : ''}`}
                    >
                      <Image
                        src={optimizedImageSrc}
                        alt={imageAlt}
                        fill
                        className={styles.projectImage}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        style={{ objectFit: 'cover' }}
                      />
                      <div className={styles.imageOverlay}></div>
                    </div>
                  );
                })}
              </div>

              {/* Project Information */}
              <div className={styles.projectInfo}>
                <h3 className={styles.projectTitle}>{project.title}</h3>
                <p className={styles.projectDescription}>{project.description}</p>
                
                <div className={styles.projectMeta}>
                  {project.location && (
                    <div className={styles.metaItem}>
                      <span className={styles.metaLabel}>Location:</span>
                      <span className={styles.metaValue}>{project.location}</span>
                    </div>
                  )}
                  {project.projectType && (
                    <div className={styles.metaItem}>
                      <span className={styles.metaLabel}>Type:</span>
                      <span className={styles.metaValue}>{project.projectType}</span>
                    </div>
                  )}
                  {project.completedDate && (
                    <div className={styles.metaItem}>
                      <span className={styles.metaLabel}>Completed:</span>
                      <span className={styles.metaValue}>{project.completedDate}</span>
                    </div>
                  )}
                  {project.clientName && (
                    <div className={styles.metaItem}>
                      <span className={styles.metaLabel}>Client:</span>
                      <span className={styles.metaValue}>{project.clientName}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

const YouMayAlsoLike: React.FC<YouMayAlsoLikeProps> = ({
  collections,
  currentCollectionId,
  title = "You May Also Like",
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Filter out the current collection and get related collections
  const relatedCollections = collections.filter(collection => collection.id !== currentCollectionId);

  if (relatedCollections.length === 0) {
    return null;
  }

  // Show 3 collections at a time
  const itemsPerPage = 3;
  const totalPages = Math.ceil(relatedCollections.length / itemsPerPage);
  const currentCollections = relatedCollections.slice(currentIndex, currentIndex + itemsPerPage);

  const handlePrevious = () => {
    setCurrentIndex(prev => Math.max(0, prev - itemsPerPage));
  };

  const handleNext = () => {
    setCurrentIndex(prev => Math.min(relatedCollections.length - itemsPerPage, prev + itemsPerPage));
  };

  return (
    <section className={`${styles.youMayAlsoLike} ${className}`}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>{title}</h2>
        </div>

        <div className={styles.carouselContainer}>
          {/* Navigation Buttons */}
          <button
            className={`${styles.navButton} ${styles.prevButton}`}
            onClick={handlePrevious}
            disabled={currentIndex === 0}
            aria-label="Previous collections"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M15 18l-6-6 6-6"/>
            </svg>
          </button>

          {/* Collections Grid */}
          <div className={styles.collectionsGrid}>
            {currentCollections.map((collection) => {
              const imageSrc = collection.images && collection.images.length > 0
                ? collection.images[0]
                : "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?w=800&h=600&fit=crop&crop=center";

              const optimizedImageSrc = getOptimizedImageUrl(imageSrc, 'card');

              return (
                <Link
                  key={collection.id}
                  href={`/collections/${collection.id}`}
                  className={styles.collectionCard}
                >
                  <div className={styles.cardImageContainer}>
                    <Image
                      src={optimizedImageSrc}
                      alt={collection.name}
                      fill
                      className={styles.cardImage}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      style={{ objectFit: 'cover' }}
                    />
                    <div className={styles.cardOverlay}></div>
                    <div className={styles.cardContent}>
                      <h3 className={styles.cardTitle}>{collection.name}</h3>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>

          <button
            className={`${styles.navButton} ${styles.nextButton}`}
            onClick={handleNext}
            disabled={currentIndex + itemsPerPage >= relatedCollections.length}
            aria-label="Next collections"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 18l6-6-6-6"/>
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default StaticCompletedProjects;
export { YouMayAlsoLike };
