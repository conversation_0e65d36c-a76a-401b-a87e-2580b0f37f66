'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getOptimizedImageUrl } from '@/utils/cloudinaryUtils';
import { Collection } from '@/services/types/entities';
import styles from './YouMayAlsoLike.module.css';

interface YouMayAlsoLikeProps {
  collections: Collection[];
  currentCollectionId: number;
  title?: string;
  className?: string;
}

const YouMayAlsoLike: React.FC<YouMayAlsoLikeProps> = ({
  collections,
  currentCollectionId,
  title = "You May Also Like",
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  // Filter out the current collection and get related collections
  const relatedCollections = collections.filter(collection => collection.id !== currentCollectionId);
  
  if (relatedCollections.length === 0) {
    return null;
  }

  // Show 3 collections at a time
  const itemsPerPage = 3;
  const currentCollections = relatedCollections.slice(currentIndex, currentIndex + itemsPerPage);

  const handlePrevious = () => {
    setCurrentIndex(prev => Math.max(0, prev - itemsPerPage));
  };

  const handleNext = () => {
    setCurrentIndex(prev => Math.min(relatedCollections.length - itemsPerPage, prev + itemsPerPage));
  };

  return (
    <section className={`${styles.youMayAlsoLike} ${className}`}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>{title}</h2>
        </div>

        <div className={styles.carouselContainer}>
          {/* Navigation Buttons */}
          <button 
            className={`${styles.navButton} ${styles.prevButton}`}
            onClick={handlePrevious}
            disabled={currentIndex === 0}
            aria-label="Previous collections"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M15 18l-6-6 6-6"/>
            </svg>
          </button>

          {/* Collections Grid */}
          <div className={styles.collectionsGrid}>
            {currentCollections.map((collection) => {
              const imageSrc = collection.images && collection.images.length > 0
                ? collection.images[0]
                : "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?w=800&h=600&fit=crop&crop=center";
              
              const optimizedImageSrc = getOptimizedImageUrl(imageSrc, 'card');

              return (
                <Link
                  key={collection.id}
                  href={`/collections/${collection.id}`}
                  className={styles.collectionCard}
                >
                  <div className={styles.cardImageContainer}>
                    <Image
                      src={optimizedImageSrc}
                      alt={collection.name}
                      fill
                      className={styles.cardImage}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      style={{ objectFit: 'cover' }}
                    />
                    <div className={styles.cardOverlay}></div>
                    <div className={styles.cardContent}>
                      <h3 className={styles.cardTitle}>{collection.name}</h3>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>

          <button 
            className={`${styles.navButton} ${styles.nextButton}`}
            onClick={handleNext}
            disabled={currentIndex + itemsPerPage >= relatedCollections.length}
            aria-label="Next collections"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 18l6-6-6-6"/>
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default YouMayAlsoLike;
