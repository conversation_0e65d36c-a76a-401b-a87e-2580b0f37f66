/* Magazine-Style Collection Page */
.collectionPage {
  min-height: 100vh;
  background: #ffffff;
  color: #1f2937;
  padding-top: 4.2rem;
}

/* New Collection Page Design (4 Sections) */
.newCollectionPage {
  min-height: 100vh;
  background: #ffffff;
  color: #1f2937;
  padding-top: 4.2rem;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Ensure smooth transitions between sections */
.newCollectionPage > * {
  position: relative;
  z-index: 1;
}

/* Add subtle shadows between sections for depth */
.newCollectionPage > *:not(:first-child) {
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 2rem;
}

/* Hero Section Styling */
.heroSection {
  background: #fafafa;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #1e40af;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1e40af;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  text-align: center;
  padding: 4rem 2rem;
  color: #1e40af;
}

.errorContainer h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Products Section */
.productsSection {
  padding: 4rem 0;
  background: #ffffff;
}

.productsContainer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.sectionSubtitle {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Products Grid */
.productsGrid {
  margin-top: 2rem;
}

/* Collection Header */
.collectionHeader {
  margin-bottom: 3rem;
  padding: 2rem;
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
}

.headerContent {
  text-align: center;
}

.collectionTitle {
  font-size: 3rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.collectionDescription {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 1.5rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.collectionMeta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.productCount {
  font-weight: 600;
  color: #1e40af;
  background: white;
  padding: 0.5rem 1rem;
  border: 2px solid #1e40af;
  border-radius: 0; /* Sharp corners */
}

.collectionLevel {
  font-weight: 600;
  color: #666;
  background: white;
  padding: 0.5rem 1rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
}

/* Filter Section */
.filterSection {
  margin-bottom: 2rem;
  background: white;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
}

.searchBar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #fafafa;
  border-bottom: 1px solid #ddd;
}

.searchInput {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 1rem;
  color: #666;
  z-index: 1;
}

.searchField {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;
}

.searchField:focus {
  outline: none;
  border-color: #1e40af;
}

.filterToggle {
  background: #1f2937;
  color: white;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.875rem;
}

.filterToggle:hover {
  background: #374151;
  transform: translateY(-1px);
}

.filterToggle.active {
  background: #374151;
}

.resultsCount {
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
}

/* Advanced Filters */
.advancedFilters {
  padding: 2rem;
  background: white;
  border-top: 1px solid #ddd;
}

.filterGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  align-items: end;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filterLabel {
  font-weight: 600;
  color: #1e40af;
  font-size: 0.9rem;
}

.priceRange {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.priceInput {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  font-size: 0.9rem;
}

.priceInput:focus {
  outline: none;
  border-color: #1e40af;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1e40af;
  cursor: pointer;
}

.checkbox {
  width: 18px;
  height: 18px;
  accent-color: #1e40af;
}

.sortControls {
  display: flex;
  gap: 0.5rem;
}

.sortSelect {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  background: white;
  font-size: 0.9rem;
}

.sortSelect:focus {
  outline: none;
  border-color: #1e40af;
}

.sortDirection {
  background: #1e40af;
  color: white;
  border: 2px solid #1e40af;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.sortDirection:hover {
  background: white;
  color: #1e40af;
}

.sortDirection.desc {
  transform: rotate(180deg);
}

.clearFilters {
  background: #dc3545;
  color: white;
  border: 2px solid #dc3545;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
  width: 100%;
}

.clearFilters:hover {
  background: white;
  color: #dc3545;
}

/* Products Section */
.productsSection {
  margin-top: 2rem;
}

/* ===== Level-3 Dynamic Sections ===== */
.dynamicSection {
  padding: 2.5rem 0;
  background: #ffffff;
}

.twoCol {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: stretch; /* keep rows aligned; prevents diagonal feel */
  /* Ensure section height exceeds image height */
  min-height: 460px; /* image is 420px; +40px breathing room */
}

/* Explicit column ordering handled in markup via grid-areas */
.twoColAreasRight { grid-template-areas: 'content image'; }
.twoColAreasLeft  { grid-template-areas: 'image content'; }
/* Flip helper toggles areas without changing DOM order */
.flipped.twoColAreasRight { grid-template-areas: 'image content'; }
.flipped.twoColAreasLeft  { grid-template-areas: 'content image'; }
.colContent { grid-area: content; }
.colImage   { grid-area: image; }

/* Mobile: stack image then content for both orientations */
@media (max-width: 900px) {
  .twoCol { grid-template-columns: 1fr; }
  .twoColAreasRight,
  .twoColAreasLeft,
  .flipped.twoColAreasRight,
  .flipped.twoColAreasLeft { grid-template-areas: 'image' 'content'; }
}

/* Explicit column ordering handled in markup; no RTL needed */

.imageWrap {
  position: relative;
  width: 100%;
  height: 380px;
  overflow: hidden;
  border-radius: 0.5rem;
}

.sectionImage { object-fit: cover; }

.dynamicTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.dynamicDescription {
  color: #374151;
  line-height: 1.7;
  margin-bottom: 1rem;
}

.dynamicButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #153B4D;
  color: #fff;
  padding: 0.6rem 1rem;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 600;
}

.dynamicButton:hover { opacity: 0.95; }

@media (max-width: 900px) {
  .twoCol { grid-template-columns: 1fr; }
  .imageWrap { height: 320px; }
}


/* Collage grid for level 3 images */
.collageGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
/* Each row adapts between 1, 2, or 3 columns */
.collageRow {
  display: grid;
  gap: 1rem;
}
.cols1 { grid-template-columns: 1fr; }
.cols2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.cols3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

.collageItem { position: relative; }

/* Responsive behavior: on small screens collapse to 1 or 2 columns */
@media (max-width: 900px) {
  .cols3 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}
@media (max-width: 640px) {
  .cols2, .cols3 { grid-template-columns: 1fr; }
}


/* Collage hover effect for interactivity */
.collageHover { transition: transform 200ms ease, opacity 200ms ease; }
.collageHover:hover { transform: scale(1.01); opacity: 0.98; }

/* Removed modal overlay feature for collage images */
.modalBackdrop, .modalContent, .modalImageWrap, .modalImage, .modalClose { display: none; }
@media (max-width: 640px) { .modalImageWrap { display: none; } }


/* ===== Section 3: Feature Hero ===== */
.section3Hero {
  position: relative;
  background: #0b2230; /* deep navy to match site accents */
  color: #e5ecf2;
  border-radius: 16px;
  padding: 2rem;
  overflow: hidden;
}

/* motion polish on hover */
.section3Hero:hover .section3ImageOutline {
  border-color: rgba(255,255,255,0.35);
}


.section3Hero::after {
  content: "";
  position: absolute;
  inset: 0;
  background: radial-gradient(1200px 400px at -10% 110%, rgba(21, 59, 77, 0.6), transparent),
              radial-gradient(900px 300px at 120% -20%, rgba(30, 64, 175, 0.35), transparent);
  pointer-events: none;
}

.section3ContentCol { padding: 1rem 0; }
.section3ImageCol { position: relative; }

.section3Badge {
  display: inline-block;
  padding: 0.35rem 0.7rem;
  border-radius: 999px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: #c7d2fe;
  font-size: 0.75rem;
  letter-spacing: 0.08em;
  text-transform: uppercase;
}

.section3Title {
  margin: 0.75rem 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  letter-spacing: 0.01em;
}

.section3Description {
  color: #c7d0d9;
  line-height: 1.75;
  max-width: 56ch;
}

.section3Stats {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 0.75rem;
  margin: 1rem 0 1.25rem;
}

.section3StatCard {
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 12px;
  padding: 0.9rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-start;
}
.section3StatCard strong { color: #fff; font-size: 1.05rem; }
.section3StatCard span { color: #c7d0d9; font-size: 0.85rem; }

.section3Ctas {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.section3PrimaryCta {
  background: #1e40af;
  color: #fff;
  text-decoration: none;
  padding: 0.6rem 1rem;
  border-radius: 999px;
  font-weight: 700;
}
.section3PrimaryCta:hover { background: #153b4d; }

.section3GhostCta {
  background: transparent;
  color: #e5ecf2;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-decoration: none;
  padding: 0.55rem 0.9rem;
  border-radius: 999px;
  font-weight: 600;
}
.section3GhostCta:hover { background: rgba(255,255,255,0.06); }

.section3ImageWrap {
  position: relative;
  width: 100%;
  height: 420px;
  border-radius: 18px;
  overflow: hidden;
}
.section3Image { object-fit: cover; }
.section3ImageOutline {
  position: absolute;
  inset: 10px;
  border-radius: 14px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1;
}

@media (max-width: 1024px) {
  .section3Title { font-size: 1.75rem; }
  .section3ImageWrap { height: 360px; }
}
@media (max-width: 900px) {
  .section3Hero { padding: 1.25rem; }
}
@media (max-width: 768px) {
  .section3Stats { grid-template-columns: 1fr 1fr 1fr; }
  .section3ImageWrap { height: 320px; }
}
@media (max-width: 480px) {
  .section3Stats { grid-template-columns: 1fr 1fr; }
}


/* Responsive Design */
@media (max-width: 1024px) {
  .collectionTitle {
    font-size: 2.5rem;
  }

  .filterGrid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .collectionHeader {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .collectionTitle {
    font-size: 2rem;
  }

  .collectionDescription {
    font-size: 1rem;
  }

  .collectionMeta {
    flex-direction: column;
    gap: 1rem;
  }

  .searchBar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .searchInput {
    width: 100%;
  }

  .filterToggle {
    width: 100%;
    justify-content: center;
  }

  .filterGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .priceRange {
    flex-direction: column;
    align-items: stretch;
  }

  .sortControls {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .collectionTitle {
    font-size: 1.75rem;
  }

  .advancedFilters {
    padding: 1rem;
  }

  .searchField {
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  }

  .filterToggle {
    padding: 0.75rem 1rem;
  }
}

/* Additional Magazine-Style Responsive Design */
@media (max-width: 1024px) {
  .sectionTitle {
    font-size: 2.25rem;
  }
}

@media (max-width: 768px) {
  .productsSection {
    padding: 3rem 0;
  }

  .productsContainer {
    padding: 0 1rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionSubtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .productsSection {
    padding: 2rem 0;
  }

  .sectionTitle {
    font-size: 1.75rem;
  }

  .sectionSubtitle {
    font-size: 0.95rem;
  }
}

/* Collections Section (for Level 1 & 2 collections) */
.collectionsSection {
  padding: 4rem 0;
  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);
}

.collectionsContainer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.collectionsGrid {
  margin-top: 3rem;
}

.collectionCard {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.collectionCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Empty State for Collections */
.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.emptyIcon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  color: #d1d5db;
}

.emptyState h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.emptyState p {
  font-size: 1rem;
  line-height: 1.6;
}

/* ====== ID=2 Exclusive Sections ====== */
.id2Subcategories {
  padding: 3rem 0;
  background: #ffffff;
}

.id2TwoColumn {
  display: grid;
  grid-template-columns: 260px 1fr;
  gap: 2rem;
  align-items: start;
}

.id2Sidebar {
  border-right: 1px solid #e5e7eb;
  padding-right: 1rem;
}

.sidebarHeading {
  font-size: 0.9rem;
  letter-spacing: 0.02em;
  color: #6b7280;
  text-transform: uppercase;
  margin-bottom: 0.75rem;
}

.categoryNav { margin: 0; }
.categoryList { list-style: none; padding: 0; margin: 0; display: flex; flex-direction: column; gap: 0.5rem; }
.categoryItem { margin: 0; }
.categoryLink { color: #1f2937; text-decoration: none; font-size: 0.95rem; }
.categoryLink:hover { text-decoration: underline; }

/* Right-aligned 2-column grid */
.id2Grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 1.5rem;
}

.id2Card {
  display: block;
}

/* Recommendations */
.recommendationsSection {
  padding: 3rem 0 4rem;
  background: #f9fafb;
}

.recsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.recsTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.recsNav {
  display: flex;
  gap: 0.5rem;
}

.recsButton {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d1d5db;
  background: #fff;
  color: #1f2937;
  cursor: pointer;
}

.recsScroller {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  padding-bottom: 0.5rem;
}

.recsItem {
  flex: 0 0 auto;
  width: 320px;
  scroll-snap-align: start;
}

/* Responsive */
@media (max-width: 1024px) {
  .id2TwoColumn { grid-template-columns: 1fr; }
  .id2Sidebar { display: none; }
}

@media (max-width: 640px) {
  .id2Grid { grid-template-columns: 1fr; }
  .recsItem { width: 85vw; }
}

/* Full-bleed image overlay card */
.imageCard {
  display: block;
  text-decoration: none;
  color: inherit;
}

.cardImageWrap {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 0.75rem;
  margin: 0;
}
/* Static content section for ID=2 */
.staticContentSection {
  padding: 2.5rem 0 2rem;
  background: #ffffff;
}

.staticContentWrap {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.staticBlock {
  padding: 1.25rem 0;
  border-top: 1px solid #e5e7eb;
}

.staticHeader {
  /* text-align: center;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
  font-size: 1.55rem;
  color: #253245ff;
  margin-bottom: 1rem; */

  font-size: 3.5rem;
  font-weight: 250;
  color: #342883ff;
  margin-bottom: 1rem;
  letter-spacing: 0.02em;
  line-height: 1.2;
  text-transform: uppercase;
  font-family: 'Georgia', serif;
}

.staticParagraph {
  margin: 0.5rem 0 0 0;
  color: #4b5563;
  line-height: 1.7;
}

@media (max-width: 768px) {
  .staticHeader { font-size: 1.125rem; }
}



/* Ensure the image fills entire card area */
.cardImage {
  object-fit: cover;
  z-index: 0;
}

/* Dark gradient and text overlay */
.cardOverlay {
 position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;   /* stack title & description */
  justify-content: flex-end; /* keep them at bottom */
  align-items: flex-start;  /* align left */
  padding: 1rem 1.25rem;
  background: linear-gradient(to top, rgba(0,0,0,0.55) 0%, rgba(0,0,0,0.1) 60%, rgba(0,0,0,0) 100%);
  z-index: 1;
  transition: all 0.3s ease;

}

  .cardTitle {
    margin: 0;
    font-weight: 700;
    font-size: 1.125rem;
    color: #fff;
    line-height: 1.3;
    text-align: left;  /* Add this */
    width: 100%;
    display: block;
  }


  .cardDescription {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    line-height: 1.5;
    margin: 0;
    /* display: -webkit-box; */
      width: 100%;       /* Add this */
    display: block;    /* Change to block */
    text-align: left;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    transition: opacity 0.3s ease;
      margin-top: 0.25rem; /* add a little spacing */

  }

/* Remove extra white space in grid items */
.id2Card, .recsItem { margin: 0; padding: 0; }

.imageCard:hover .cardOverlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.95) 0%,
    rgba(0, 0, 0, 0.7) 30%,
    rgba(0, 0, 0, 0.1) 100%
  );
}

.imageCard:hover .cardDescription {
  opacity: 1;
}


@media (max-width: 1024px) {
  .cardImageWrap {
    height: 350px; /* Slightly reduced height for tablets */
  }
    .cardOverlay {
    padding: 1.5rem;
  }

  .cardTitle {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .cardDescription {
    font-size: 0.95rem;
    line-height: 1.4;
  }
}

@media (max-width: 768px) {
  .cardImageWrap {
    height: 300px; /* Further reduced height for mobile */
  }
    .cardOverlay {
    padding: 1.25rem;
  }

  .cardTitle {
    font-size: 1.125rem;
  }

  .cardDescription {
    font-size: 0.9rem;
    -webkit-line-clamp: 3;
  }
}

@media (max-width: 480px) {
  .cardImageWrap {
    height: 250px; /* Smallest height for small mobile devices */
  }
}

