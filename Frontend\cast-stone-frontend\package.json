{"name": "cast-stone-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"animate.css": "^4.1.1", "framer-motion": "^12.23.12", "leaflet": "^1.9.4", "lucide-react": "^0.539.0", "masonic": "^4.1.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "swiper": "^11.2.10", "wow.js": "^1.2.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/leaflet": "^1.9.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "5.8.3"}}