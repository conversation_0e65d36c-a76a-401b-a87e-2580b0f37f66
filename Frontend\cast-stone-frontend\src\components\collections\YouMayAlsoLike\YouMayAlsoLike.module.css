/* You May Also Like Section Styles */
.youMayAlsoLike {
  padding: 6rem 0;
  background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 1rem;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: #ffffff;
}

.carouselContainer {
  display: flex;
  align-items: center;
  gap: 2rem;
  position: relative;
}

.collectionsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  flex: 1;
  min-height: 400px;
}

.collectionCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.collectionCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.cardImageContainer {
  position: relative;
  height: 400px;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
}

.cardImage {
  transition: transform 0.3s ease;
}

.collectionCard:hover .cardImage {
  transform: scale(1.05);
}

.cardOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 1;
}

.cardContent {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  z-index: 2;
}

.cardTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.3;
}

.navButton {
  background: #3182ce;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
  flex-shrink: 0;
}

.navButton:hover:not(:disabled) {
  background: #2c5aa0;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(49, 130, 206, 0.4);
}

.navButton:disabled {
  background: #4a5568;
  cursor: not-allowed;
  opacity: 0.5;
  box-shadow: none;
}

.navButton svg {
  width: 20px;
  height: 20px;
}

.prevButton {
  order: -1;
}

.nextButton {
  order: 1;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .youMayAlsoLike {
    padding: 4rem 0;
  }

  .title {
    font-size: 2rem;
  }

  .collectionsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .cardImageContainer {
    height: 350px;
  }

  .cardTitle {
    font-size: 1.3rem;
  }

  .cardContent {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .youMayAlsoLike {
    padding: 3rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .header {
    margin-bottom: 3rem;
  }

  .title {
    font-size: 1.8rem;
  }

  .carouselContainer {
    flex-direction: column;
    gap: 1.5rem;
  }

  .collectionsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .cardImageContainer {
    height: 300px;
  }

  .cardTitle {
    font-size: 1.2rem;
  }

  .cardContent {
    padding: 1.2rem;
  }

  .navButton {
    width: 45px;
    height: 45px;
  }

  .navButton svg {
    width: 18px;
    height: 18px;
  }

  .prevButton,
  .nextButton {
    order: 0;
  }

  .carouselContainer {
    position: relative;
  }

  .prevButton {
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    z-index: 3;
  }

  .nextButton {
    position: absolute;
    top: 50%;
    right: -10px;
    transform: translateY(-50%);
    z-index: 3;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.5rem;
  }

  .cardImageContainer {
    height: 250px;
  }

  .cardTitle {
    font-size: 1.1rem;
  }

  .cardContent {
    padding: 1rem;
  }

  .navButton {
    width: 40px;
    height: 40px;
  }

  .navButton svg {
    width: 16px;
    height: 16px;
  }

  .prevButton {
    left: -5px;
  }

  .nextButton {
    right: -5px;
  }
}
