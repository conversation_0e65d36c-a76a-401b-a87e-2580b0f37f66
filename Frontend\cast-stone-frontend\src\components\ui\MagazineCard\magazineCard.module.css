/* Magazine Card - Clean & Professional Design */
.cardLink,
.cardButton {
  display: block;
  text-decoration: none;
  color: inherit;
  border: none;
  background: none;
  padding: 0;
  cursor: pointer;
  width: 100%;
}

.magazineCard {
  background: #ffffff;
  border-radius: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;

  /* Perfect Equal Height Cards */
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 450px;
}


.cardLink:hover .magazineCard,
.cardButton:hover .magazineCard {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Image Container - Fixed Height for Perfect Symmetry */
.imageContainer {
  position: relative;
  width: 100%;
  height: 250px;
  max-height: 200px;
  overflow: hidden;
  flex-shrink: 0; /* Prevent image from shrinking */
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.cardLink:hover .image,
.cardButton:hover .image {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cardLink:hover .imageOverlay,
.cardButton:hover .imageOverlay {
  opacity: 1;
}

.badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: #ffffff;
  color: #1f2937;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Content - Perfect Symmetrical Layout */
.content {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: left;
  min-height: 150px; /* Ensure consistent content area height */
}

.title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  min-height: 2.8rem; /* Ensure consistent title height for 2 lines */
  display: flex;
  align-items: flex-start;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.description {
  font-size: 0.95rem;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 4.8rem; /* Ensure consistent description height for 3 lines */
  flex-grow: 1;
}

.priceContainer {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.price {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
}

.originalPrice {
  font-size: 0.95rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.additionalContent {
  margin-top: auto;
  padding-top: 1rem;
}

/* Variants */
.featured {
  grid-column: span 2;
}

.featured .imageContainer {
  height: 350px;
  flex-shrink: 0;
}

.featured .content {
  padding: 2.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  min-height: 180px;
}

.featured .title {
  font-size: 1.5rem;
  min-height: 3.2rem;
}

.compact .imageContainer {
  height: 200px;
}

.compact .content {
  padding: 1.5rem;
}

.compact .title {
  font-size: 1.125rem;
}

.compact .description {
  -webkit-line-clamp: 2;
}

/* Image Position Variants */
.left,
.right {
  display: flex;
  height: 300px;
}

.left .imageContainer,
.right .imageContainer {
  width: 50%;
  height: 100%;
}

.left .content,
.right .content {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.right {
  flex-direction: row-reverse;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .featured {
    grid-column: span 1;
  }
  
  .featured .imageContainer {
    height: 300px;
    flex-shrink: 0;
  }
  
  .featured .content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 150px;
  }
  
  .featured .title {
    font-size: 1.375rem;
  }
}

@media (max-width: 768px) {
  .left,
  .right {
    flex-direction: column;
    height: auto;
  }
  
  .left .imageContainer,
  .right .imageContainer {
    width: 100%;
    height: 200px;
  }
  
  .left .content,
  .right .content {
    width: 100%;
  }
  
  .content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 130px;
  }
  
  .title {
    font-size: 1.125rem;
  }
  
  .description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .imageContainer {
    height: 200px;
    flex-shrink: 0;
  }

  .content {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 120px;
  }
  
  .title {
    font-size: 1rem;
    min-height: 2.4rem;
  }

  .description {
    font-size: 0.875rem;
    -webkit-line-clamp: 2;
    min-height: 3.2rem;
  }
}
